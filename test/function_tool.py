from agen.tools import FunctionTool,HttpTool
from agen.tools.code_executor import Alias
from pydantic import BaseModel
from agen.base import CancellationToken
import asyncio


def calculator(operation: str, a: float, b: float) -> str:
    """执行基本数学运算

    Args:
        operation: 运算类型 (add, subtract, multiply, divide)
        a: 第一个数字
        b: 第二个数字

    Returns:
        运算结果的字符串表示
    """
    # import pandas as pd
    # import numpy as np
    try:
        if operation == "add":
            result = a + b
        elif operation == "subtract":
            result = a - b
        elif operation == "multiply":
            result = a * b
        elif operation == "divide":
            if b == 0:
                return "错误：除数不能为零"
            result = a / b
        else:
            return f"错误：不支持的运算类型 {operation}"

        return f"{a} {operation} {b} = {result}"
    except Exception as e:
        return f"计算错误：{str(e)}"

cal_tool = FunctionTool(
    func=calculator,
    description="一个数学计算器，支持加减乘除",
    name="简单计算器",
    global_imports = [Alias("pandas", "pd")],
    strict=True
)


# 参数的作用是启用严格模式，用于控制工具参数的验证规则：所有参数必须是必需的：不允许有默认值参数
# 不允许额外属性： additionalProperties 必须为 False
# 确保参数完整性：所有定义的属性都必须在  required 列表中


# class CalculatorArgs(BaseModel):
#     operation: str
#     a: float
#     b: float

async def main():
    args_json = {"operation": "add", "a": 1.0, "b": 2.0}
    # run_args = CalculatorArgs(operation="add", a=1.0, b=2.0)
    result = await cal_tool.run_json(args=args_json, cancellation_token=CancellationToken())
    print(result)

asyncio.run(main())

print(cal_tool.name)
print(cal_tool.description)
cal_tool_config = cal_tool._to_config()
print(cal_tool_config)

print(type(cal_tool_config))
print(cal_tool_config.model_dump())
print(type(cal_tool_config.model_dump()))
print(cal_tool.dump_component())


