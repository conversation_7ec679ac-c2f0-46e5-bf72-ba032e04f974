import asyncio
from agen.models.openai import OpenAIChatCompletionClient
from agen.agents import Assistant<PERSON>gent
from agen.teams import RoundRobinGroupChat
from agen.conditions import TextMentionTermination
from agen.ui import Console


async def main() -> None:
    model_client = OpenAIChatCompletionClient(
    model="Qwen3-32B",
    base_url="http://172.20.15.190:9005/v1",
    api_key="",
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        # "family": ModelFamily.R1,
        "structured_output": True,
    }
    )

    async def get_weather(location: str) -> str:
        return f"The weather in {location} is sunny."

    assistant = Assistant<PERSON>gent(
        "Assistant",
        model_client=model_client,
        tools=[get_weather],
    )
    termination = TextMentionTermination("TERMINATE")
    team = RoundRobinGroupChat([assistant], termination_condition=termination)
    await <PERSON>sole(team.run_stream(task="What's the weather in New York?"))


asyncio.run(main())