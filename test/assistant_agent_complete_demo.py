"""
完整的AssistantAgent演示程序
"""

import asyncio
from datetime import datetime

# 核心模块导入
from agen.agents import AssistantAgent
from agen.models.openai import OpenAIChatCompletionClient
from agen.models import ModelFamily
from agen.tools import FunctionTool,HttpTool
from agen.ui import Console
from agen.memory import MemoryContent, MemoryMimeType, ListMemory


def calculator(operation: str, a: float, b: float) -> str:
    """执行基本数学运算
    
    Args:
        operation: 运算类型 (add, subtract, multiply, divide)
        a: 第一个数字
        b: 第二个数字
    
    Returns:
        运算结果的字符串表示
    """
    try:
        if operation == "add":
            result = a + b
        elif operation == "subtract":
            result = a - b
        elif operation == "multiply":
            result = a * b
        elif operation == "divide":
            if b == 0:
                return "错误：除数不能为零"
            result = a / b
        else:
            return f"错误：不支持的运算类型 {operation}"
        
        return f"{a} {operation} {b} = {result}"
    except Exception as e:
        return f"计算错误：{str(e)}"


def get_current_time() -> str:
    """获取当前时间
    
    Returns:
        当前时间的字符串表示
    """
    return f"当前时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"



def weather_info(city: str) -> str:
    """获取天气信息（模拟）
    
    Args:
        city: 城市名称
    
    Returns:
        天气信息
    """
    # 模拟天气数据
    weather_data = {
        "北京": "晴天，温度 15°C",
        "上海": "多云，温度 18°C", 
        "广州": "小雨，温度 22°C",
        "深圳": "晴天，温度 25°C"
    }
    
    return weather_data.get(city, f"抱歉，暂无{city}的天气信息")


# http tool
base64_schema = {
    "type": "object",
    "properties": {
        "value": {"type": "string", "description": "The base64 value to decode"},
    },
    "required": ["value"],
}

# Create an HTTP tool for the httpbin API
base64_tool = HttpTool(
    name="base64_decode",
    description="base64 decode a value",
    scheme="https",
    host="httpbin.org",
    port=443,
    path="/base64/{value}",
    method="GET",
    json_schema=base64_schema,
)



async def demonstrate_basic_features():
    """演示基础功能"""
    print("=== 基础功能演示 ===")
    
    model_client = OpenAIChatCompletionClient(
    model="Qwen3-32B",
    base_url="http://*************:9005/v1",
    api_key="",
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.R1,
        "structured_output": True,
    }
    )
    
    # 创建工具
    tools = [
        FunctionTool(calculator, description="数学计算器"),
        FunctionTool(get_current_time, description="获取当前时间"),
        FunctionTool(weather_info, description="获取天气信息"),
        base64_tool # http 接口
    ]
    
    # 创建智能体
    agent = AssistantAgent(
        name="demo_assistant",
        model_client=model_client,
        tools=tools,
        description="一个功能丰富的演示助手",
        system_message="你是一个有用的AI助手。使用提供的工具来帮助用户完成任务。用中文回复。",
        model_client_stream=True,
        reflect_on_tool_use=True,
        max_tool_iterations=3,
    )
    
    # 测试基础功能
    print("\n1. 数学计算测试：")
    await Console(agent.run_stream(task="计算 15 + 27 的结果"))

    # print("\n2. 时间查询测试：")
    # await Console(agent.run_stream(task="现在几点了？"))

    print("\n3. 天气查询测试：")
    await Console(agent.run_stream(task="北京的天气怎么样？"))

    # print("\n4. Base64解码测试：")
    # await Console(agent.run_stream(task="解码 base64 编码的字符串 'SGVsbG8gd29ybGQ='"))


async def demonstrate_memory():
    """演示内存功能"""
    print("\n=== 内存功能演示 ===")
    
    model_client = OpenAIChatCompletionClient(
    model="Qwen3-32B",
    base_url="http://*************:9005/v1",
    api_key="",
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.R1,
        "structured_output": True,
    }
    )
    
    # 创建ListMemory实例
    memory = ListMemory(name="demo_memory")

    # 添加一些记忆
    await memory.add(MemoryContent(
        content="用户喜欢数学计算和天气查询",
        mime_type=MemoryMimeType.TEXT,
        metadata={"type": "preference"}
    ))

    await memory.add(MemoryContent(
        content="上次计算结果：15 + 27 = 42",
        mime_type=MemoryMimeType.TEXT,
        metadata={"type": "calculation_history"}
    ))
    
    # 创建带内存的智能体
    agent = AssistantAgent(
        name="memory_assistant",
        model_client=model_client,
        tools=[FunctionTool(calculator, description="数学计算器")],
        memory=[memory],
        description="具有记忆功能的助手",
        system_message="你是一个有记忆的AI助手。记住用户的偏好和历史交互。用中文回复。",
        model_client_stream=True,
    )
    
    print("\n记忆测试：")
    await Console(agent.run_stream(task="你还记得我上次的计算结果吗？"))


async def main():
    """主程序"""
    print("🤖 AssistantAgent 完整功能")
    print("=" * 50)
    
    try:
        # 演示各种功能
        await demonstrate_basic_features()
        await demonstrate_memory()
        
        print("\n✅ 所有功能完成！")
        
    except Exception as e:
        print(f"❌ 过程中出现错误：{e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
