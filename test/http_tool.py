from agen.tools import FunctionTool,HttpTool
from agen.tools.code_executor import Alias
from pydantic import BaseModel
from agen.base import CancellationToken
import asyncio


# http tool
base64_schema = {
    "type": "object",
    "properties": {
        "value": {"type": "string", "description": "The base64 value to decode"},
    },
    "required": ["value"],
}

# Create an HTTP tool for the httpbin API
base64_tool = HttpTool(
    name="base64_decode",
    description="base64 decode a value",
    scheme="https",
    host="httpbin.org",
    port=443,
    path="/base64/{value}",
    method="GET",
    json_schema=base64_schema,
)

print(base64_tool._to_config())
print(base64_tool.args_type())
print(base64_tool.return_type())
print(base64_tool.name)
print(base64_tool.description)
print(base64_tool._strict)
