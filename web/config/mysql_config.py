from typing import Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class MySQLSettings(BaseSettings):
    MYSQL_HOST: Optional[str] = 'localhost'
    MYSQL_PORT: Optional[int] = 3306
    MYSQL_USERNAME: str = Field(..., description="必须提供数据库用户名")
    MYSQL_PASSWORD: str = Field(..., description="必须提供数据库密码")

    # 不同的数据库名
    DB_NAME_AGENTS: str

    class Config:
        env_file = './web/config/.env.mysql'
        env_file_encoding = 'utf-8'

    def get_url(self, db_name: str) -> str:
        return (
            f"mysql+pymysql://{self.MYSQL_USERNAME}:{self.MYSQL_PASSWORD}"
            f"@{self.MYSQL_HOST}:{self.MYSQL_PORT}/{db_name}"
        )


    def get_asyn_url(self, db_name: str) -> str:
        return (
            f"mysql+asyncmy://{self.MYSQL_USERNAME}:{self.MYSQL_PASSWORD}"
            f"@{self.MYSQL_HOST}:{self.MYSQL_PORT}/{db_name}"
        )

    @property
    def agents_db_url(self):
        return self.get_url(self.DB_NAME_AGENTS)

    @property
    def agents_db_url_asyn(self):
        return self.get_asyn_url(self.DB_NAME_AGENTS)


if __name__ == '__main__':
    print(MySQLSettings().agents_db_url)
