import os

from fastapi import <PERSON><PERSON><PERSON>
from fastapi import HTT<PERSON>Exception
from fastapi.middleware.cors import CORSMiddleware

from web.api.v1.endpoints import agent_basic, agents_config, tools_config, models_config
from web.config.mysql_config import MySQLSettings
from web.core.exceptions import global_exception_handler
from web.core.logger import logging
from web.db.database import engine_async, Base
from .models import get_db

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", MySQLSettings().agents_db_url_asyn)

app = FastAPI(title="AI-Agent API", description="AI-Agent相关服务")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# 路由

app.include_router(agent_basic.basic_router, prefix="/v1/agents/basic", tags=["agents_basic"])

app.include_router(agents_config.router, prefix="/v1/agents", tags=["agents_config"])

app.include_router(models_config.router, prefix="/v1/models", tags=["models_config"])

app.include_router(tools_config.router, prefix="/v1/tools", tags=["tools_config"])

# 全局异常处理器
app.add_exception_handler(Exception, global_exception_handler)
app.add_exception_handler(HTTPException, global_exception_handler)


@app.on_event("startup")
async def init_db():
    async with engine_async.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)  # 创建表
    logging.info("数据库表已成功创建")

    db = get_db(DATABASE_URL)
    db.initialize_database()


# 测试接口
@app.get("/")
async def root():
    return {"message": "AI-Agent相关服务"}


@app.get("/health")
async def health_check():
    return {"status": "healthy"}
