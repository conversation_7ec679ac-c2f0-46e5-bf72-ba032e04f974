import asyncio
import json
import logging
import os
import time
from pathlib import Path
from typing import Any, AsyncGenerator, Awaitable, Callable, Dict, List, Optional, Sequence, Union

import aiofiles
import yaml
from agen.base.task import TaskResult
from agen.base.component import ComponentModel
from agen.messages import BaseAgentEvent, BaseChatMessage
from agen.agents import BaseChatAgent
from agen.base import EVENT_LOGGER_NAME, CancellationToken
from agen.base.logging import LLMCallEvent

logger = logging.getLogger(__name__)

SyncInputFunc = Callable[[str], str]
AsyncInputFunc = Callable[[str, Optional[CancellationToken]], Awaitable[str]]
InputFuncType = Union[SyncInputFunc, AsyncInputFunc]



from contextlib import contextmanager
from contextvars import ContextVar
from typing import Any, ClassVar, Generator


class RunContext:
    RUN_CONTEXT_VAR: ClassVar[ContextVar] = ContextVar("RUN_CONTEXT_VAR")

    @classmethod
    @contextmanager
    def populate_context(cls, run_id) -> Generator[None, Any, None]:
        token = RunContext.RUN_CONTEXT_VAR.set(run_id)
        try:
            yield
        finally:
            RunContext.RUN_CONTEXT_VAR.reset(token)

    @classmethod
    def current_run_id(cls) -> str:
        try:
            return cls.RUN_CONTEXT_VAR.get()
        except LookupError as e:
            raise RuntimeError("Error getting run id") from e



from pydantic import BaseModel
from agen.messages import ChatMessage,TextMessage
from agen.models import UserMessage
from typing import Literal

class MessageConfig(BaseModel):
    source: str
    content: str | ChatMessage | Sequence[ChatMessage] | None
    message_type: Optional[str] = "text"


class AgentResult(BaseModel):
    task_result: TaskResult
    usage: str
    duration: float


class LLMCallEventMessage(TextMessage):
    source: str = "llm_call_event"

    def to_text(self) -> str:
        return self.content

    def to_model_text(self) -> str:
        return self.content

    def to_model_message(self) -> UserMessage:
        raise NotImplementedError("This message type is not supported.")




class EnvironmentVariable(BaseModel):
    name: str
    value: str
    type: Literal["string", "number", "boolean", "secret"] = "string"
    description: Optional[str] = None
    required: bool = False


class RunEventLogger(logging.Handler):
    """Event logger that queues LLMCallEvents for streaming"""

    def __init__(self):
        super().__init__()
        self.events: asyncio.Queue[LLMCallEventMessage] = asyncio.Queue()

    def emit(self, record: logging.LogRecord):
        if isinstance(record.msg, LLMCallEvent):
            self.events.put_nowait(LLMCallEventMessage(content=str(record.msg)))


class AgentManager:
    """Manages agent operations including loading configs and running agents"""

    def __init__(self):
        self._agent: Optional[BaseChatAgent] = None
        self._run_context = RunContext()

    @staticmethod
    async def load_from_file(path: Union[str, Path]) -> Any:
        """Load agent configuration from JSON/YAML file"""
        path = Path(path)
        if not path.exists():
            raise FileNotFoundError(f"Config file not found: {path}")

        async with aiofiles.open(path) as f:
            content = await f.read()
            if path.suffix == ".json":
                return json.loads(content)
            elif path.suffix in (".yml", ".yaml"):
                return yaml.safe_load(content)
            raise ValueError(f"Unsupported file format: {path.suffix}")

    @staticmethod
    async def load_from_directory(directory: Union[str, Path]) -> List[Any]:
        """Load all agent configurations from a directory"""
        directory = Path(directory)
        configs: List[Any] = []
        valid_extensions = {".json", ".yaml", ".yml"}

        for path in directory.iterdir():
            if path.is_file() and path.suffix.lower() in valid_extensions:
                try:
                    config = await AgentManager.load_from_file(path)
                    configs.append(config)
                except Exception as e:
                    logger.error(f"Failed to load {path}: {e}")

        return configs

    async def _create_agent(
        self,
        agent_config: Union[str, Path, Dict[str, Any], ComponentModel],
        input_func: Optional[InputFuncType] = None,
        env_vars: Optional[List[EnvironmentVariable]] = None,
    ) -> BaseChatAgent:
        """Create agent instance from config"""
        if isinstance(agent_config, (str, Path)):
            config = await self.load_from_file(agent_config)
        elif isinstance(agent_config, dict):
            config = agent_config
        elif isinstance(agent_config, ComponentModel):
            config = agent_config.model_dump()
        else:
            raise ValueError(f"Unsupported agent_config type: {type(agent_config)}")

        # Load env vars into environment if provided
        if env_vars:
            logger.info("Loading environment variables")
            for var in env_vars:
                os.environ[var.name] = var.value

        self._agent = BaseChatAgent.load_component(config)
        return self._agent

    async def run_stream(
        self,
        task: str | BaseChatMessage | Sequence[BaseChatMessage] | None,
        agent_config: Union[str, Path, Dict[str, Any], ComponentModel],
        input_func: Optional[InputFuncType] = None,
        cancellation_token: Optional[CancellationToken] = None,
        env_vars: Optional[List[EnvironmentVariable]] = None,
    ) -> AsyncGenerator[Union[BaseAgentEvent | BaseChatMessage | LLMCallEvent, BaseChatMessage, AgentResult], None]:
        """Stream agent execution results"""
        start_time = time.time()
        agent = None

        # Setup logger correctly
        logger = logging.getLogger(EVENT_LOGGER_NAME)
        logger.setLevel(logging.INFO)
        llm_event_logger = RunEventLogger()
        logger.handlers = [llm_event_logger]  # Replace all handlers

        try:
            agent = await self._create_agent(agent_config, input_func, env_vars)

            async for message in agent.run_stream(task=task, cancellation_token=cancellation_token):
                if cancellation_token and cancellation_token.is_cancelled():
                    break

                if isinstance(message, TaskResult):
                    yield AgentResult(task_result=message, usage="", duration=time.time() - start_time)
                else:
                    yield message

                # Check for any LLM events
                while not llm_event_logger.events.empty():
                    event = await llm_event_logger.events.get()
                    yield event
        finally:
            # Cleanup - remove our handler
            if llm_event_logger in logger.handlers:
                logger.handlers.remove(llm_event_logger)

            # Ensure cleanup happens
            if hasattr(agent, "close"):
                await agent.close()

    async def run(
        self,
        task: str | BaseChatMessage | Sequence[BaseChatMessage] | None,
        agent_config: Union[str, Path, Dict[str, Any], ComponentModel],
        input_func: Optional[InputFuncType] = None,
        cancellation_token: Optional[CancellationToken] = None,
        env_vars: Optional[List[EnvironmentVariable]] = None,
    ) -> AgentResult:
        """Run agent synchronously"""
        start_time = time.time()
        agent = None

        try:
            agent = await self._create_agent(agent_config, input_func, env_vars)
            result = await agent.run(task=task, cancellation_token=cancellation_token)

            return AgentResult(task_result=result, usage="", duration=time.time() - start_time)

        finally:
            if hasattr(agent, "close"):
                await agent.close()





