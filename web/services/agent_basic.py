from datetime import datetime
from typing import Dict, Optional

from sqlalchemy import Select, func, select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from web.core.logger import logging
from web.models.agent_basic import AgentModel


class AgentBasicService:
    ''' Agent 基础信息服务：增、删、改、查 '''

    @staticmethod
    async def get_agent_basic_by_id(sess: AsyncSession, agent_id: str):
        ''' 查询，获取agent基本信息 '''
        result = await sess.execute(Select(AgentModel).where(AgentModel.id == agent_id))
        return result.scalar_one_or_none()

    @staticmethod
    async def count_agents(sess: AsyncSession) -> int:
        """
        查询 Agent 总数量
        Args:
            sess: 数据库异步会话
        Returns:
            int: Agent 的总数
        """
        logging.info("查询 Agent 总数量")
        try:
            result = await sess.execute(select(func.count(AgentModel.id)))
            return result.scalar_one()  # 返回一个整数
        except Exception as e:
            logging.exception(f"查询 Agent 数量失败: {str(e)}")
            raise RuntimeError("数据库错误")

    @staticmethod
    async def add_agent_basic(sess: AsyncSession, model: AgentModel):
        '''
        新增，添加agent基本信息（允许name重名）
        Args:
            sess: 数据库会话
            model: AgentModel 对象

        '''

        try:
            sess.add(model)
            await sess.commit()
            await sess.refresh(model)
            return model
        except IntegrityError:
            await sess.rollback()
            logging.warning(f"新增失败：唯一约束冲突，name={model.name}")
            raise ValueError("Agent 名称已存在")
        except Exception as e:
            await sess.rollback()
            logging.exception(f"新增 Agent 失败：{str(e)}")
            raise RuntimeError("数据库错误")

    @staticmethod
    async def remove_agent_basic_by_id(sess: AsyncSession, agent_id: int) -> bool:
        """
        删除：硬删除 Agent（从数据库中删除）
        Args:
            sess: 数据库会话
            agent_id: Agent ID

        Returns:
            bool: True 表示删除成功，False 表示未找到
        """
        try:
            result = await sess.execute(Select(AgentModel).where(AgentModel.id == agent_id))
            agent = result.scalar_one_or_none()

            if not agent:
                logging.warning(f"硬删除失败，Agent {agent_id} 未找到！ ")
                return False

            await sess.delete(agent)
            await sess.commit()

            logging.info(f"Agent 硬删除成功，ID: {agent_id}")
            return True

        except Exception as e:
            await sess.rollback()
            logging.exception(f"删除 Agent 失败（ID: {agent_id}）: {str(e)}")
            raise RuntimeError("数据库错误")

    @staticmethod
    async def update_agent_basic(sess: AsyncSession, agent_id: str, name: Optional[str] = None,
                                 icon: Optional[str] = None, desc: Optional[str] = None,
                                 config_dict: Optional[str] = None) -> AgentModel:
        """ 更新 Agent 基本信息（仅当字段有变化时才更新） """
        try:
            try:
                agent_id_int = int(agent_id)
            except (ValueError, TypeError):
                raise ValueError("无效的 Agent ID")

            # 查询现有记录
            result = await sess.execute(Select(AgentModel).where(AgentModel.id == agent_id_int))
            agent = result.scalar_one_or_none()

            if not agent:
                raise ValueError("Agent 不存在")

            is_modified = False  # 是否有任何字段需要更新

            # 逐字段对比并更新（仅当值不同且非 None）
            if name is not None and name != agent.name:
                agent.name = name
                is_modified = True
            if icon is not None and icon != agent.icon:
                agent.icon = icon
                is_modified = True
            if desc is not None and desc != agent.desc:
                agent.desc = desc
                is_modified = True
            if config_dict is not None and config_dict != agent.config_dict:
                agent.config_dict = config_dict
                is_modified = True

            # 如果没有任何变化，直接返回原对象（不更新 updated_at）
            if not is_modified:
                logging.info(f"Agent {agent_id} 数据无变化，跳过更新")
                return agent, False  # 直接返回，不 commit

            # 有修改：更新 updated_at 并提交
            agent.updated_at = datetime.utcnow()
            await sess.commit()
            await sess.refresh(agent)

            logging.info(f"Agent {agent_id} 更新成功，修改字段: {agent.__dict__}")
            return agent, True

        except ValueError as ve:
            logging.warning(f"更新失败：{str(ve)}")
            raise ve
        except Exception as e:
            await sess.rollback()
            logging.exception(f"更新 Agent 失败（ID: {agent_id}）: {str(e)}")
            raise RuntimeError("数据库错误")

    @staticmethod
    async def get_agent_ids_paginated(sess: AsyncSession, from_: int = 0, size: int = 10) -> Dict[str, list]:
        """
        分页查询 Agent ID 列表
        Args:
            sess: 数据库会话
            from_: 起始索引（偏移量），默认 0
            size: 返回数量，默认 10

        Returns:
            {"agentIds": ["1", "2", ...]}
        """
        try:
            from_ = max(0, from_)
            size = max(1, min(size, 100))  # 最小1，最大100

            # 查询 id 列表（按 id 升序）
            stmt = (
                select(AgentModel.id)
                .order_by(AgentModel.id)
                .offset(from_)
                .limit(size)
            )
            result = await sess.execute(stmt)
            id_list = [str(row[0]) for row in result.fetchall()]  # 转为字符串

            return {"agentIds": id_list}

        except Exception as e:
            logging.exception(f"分页查询 Agent IDs 失败: {str(e)}")
            raise RuntimeError("数据库错误")
