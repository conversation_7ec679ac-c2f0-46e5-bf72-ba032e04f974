from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from web.config.mysql_config import MySQLSettings
Base = declarative_base()

SQLALCHEMY_DATABASE_URL = MySQLSettings().agents_db_url
engine = create_engine(SQLALCHEMY_DATABASE_URL, pool_pre_ping=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


# 获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


SQLALCHEMY_DATABASE_URL_ASYNC = MySQLSettings().agents_db_url_asyn
engine_async = create_async_engine(SQLALCHEMY_DATABASE_URL_ASYNC, echo=True)
AsyncSessionLocal = async_sessionmaker(
    bind=engine_async,
    class_=AsyncSession,
    expire_on_commit=False
)


async def get_async_db():
    async with Async<PERSON>essionLocal() as session:
        yield session
