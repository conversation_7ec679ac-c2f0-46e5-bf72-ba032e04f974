from datetime import datetime
from typing import Union

from pydantic import ConfigDict, SecretStr
from sqlalchemy import Column, JSON
from sqlmodel import Field

from agen.base.component import ComponentModel
from web.models.base import BaseDBModel


class AgentsConfig(BaseDBModel, table=True):
    __table_args__ = {"mysql_engine": "InnoDB", "mysql_charset": "utf8mb4"}

    config: Union[ComponentModel, dict] = Field(
        default_factory=lambda: ComponentModel(
            provider="",
            component_type="agent",
            version=1,
            component_version=1,
            description="",
            label="",
            config={},
        ),
        sa_column=Column(JSON),
    )

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat(),
            SecretStr: lambda v: v.get_secret_value(),
        }
    )
