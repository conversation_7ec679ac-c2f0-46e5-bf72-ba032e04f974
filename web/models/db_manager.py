import json
import threading
from datetime import datetime
from typing import Any, Optional

from loguru import logger
from pydantic import BaseModel
from sqlalchemy import exc, inspect, text
from sqlmodel import Session, SQLModel, and_, create_engine, select


class CustomJSONEncoder(json.JSONEncoder):
    def default(self, o):
        if hasattr(o, "get_secret_value") and callable(o.get_secret_value):
            return o.get_secret_value()
        # Handle datetime objects
        if isinstance(o, datetime):
            return o.isoformat()
        # Handle Enum objects
        import enum

        if isinstance(o, enum.Enum):
            return o.value
        return super().default(o)


class Response(BaseModel):
    message: str
    status: bool
    data: Optional[Any] = None


class DatabaseManager:
    _init_lock = threading.Lock()

    def __init__(self,
                 engine_uri: str
                 ) -> None:

        # Configure connection arguments for MySQL
        connection_args = {
            "charset": "utf8mb4",
            "autocommit": True,
        }

        # Configure engine arguments
        engine_kwargs = {
            "connect_args": connection_args,
            "json_serializer": lambda obj: json.dumps(obj, cls=CustomJSONEncoder),
            "pool_pre_ping": True,
            "pool_recycle": 3600
        }

        self.engine = create_engine(engine_uri, **engine_kwargs)

    def initialize_database(self) -> Response:
        """
        Initialize database tables without using SchemaManager.

        Returns:
            Response: Pydantic Response model with status, message and data
        """
        if not self._init_lock.acquire(blocking=False):
            logger.warning("Database initialization already in progress")
            return Response(
                message="Database initialization already in progress",
                status=False,
                data=None
            )

        try:
            inspector = inspect(self.engine)
            tables_exist = inspector.get_table_names()

            if not tables_exist:
                logger.info("Creating database tables...")
                SQLModel.metadata.create_all(self.engine)
                logger.info("Database tables created successfully")
                return Response(
                    message="Database tables created successfully",
                    status=True,
                    data=None
                )

            logger.info("Database tables already exist")
            return Response(
                message="Database tables already exist",
                status=True,
                data=None
            )

        except Exception as e:
            error_msg = f"Database initialization failed: {str(e)}"
            logger.error(error_msg)
            return Response(
                message=error_msg,
                status=False,
                data=None
            )
        finally:
            self._init_lock.release()

    def reset_db(self, recreate_tables: bool = True) -> Response:
        """
        Reset the database by dropping all tables and optionally recreating them.

        Args:
            recreate_tables (bool): If True, recreates the tables after dropping them.
                                Set to False if you want to call create_db_and_tables() separately.

        Returns:
            Response: Pydantic Response model with status, message and data
        """
        if not self._init_lock.acquire(blocking=False):
            logger.warning("Database reset already in progress")
            return Response(
                message="Database reset already in progress",
                status=False,
                data=None
            )

        try:
            # Dispose existing connections
            self.engine.dispose()
            with Session(self.engine) as session:
                try:
                    # Disable foreign key constraints for MySQL
                    session.exec(text("SET FOREIGN_KEY_CHECKS = 0"))  # type: ignore

                    # Drop all tables
                    SQLModel.metadata.drop_all(self.engine)
                    logger.info("All tables dropped successfully")

                    # Re-enable foreign key constraints
                    session.exec(text("SET FOREIGN_KEY_CHECKS = 1"))  # type: ignore

                    session.commit()

                except Exception as e:
                    session.rollback()
                    raise e
                finally:
                    session.close()
                    self._init_lock.release()

            if recreate_tables:
                logger.info("Recreating tables...")
                self.initialize_database()

            return Response(
                message="Database reset successfully",
                status=True,
                data=None
            )

        except Exception as e:
            error_msg = f"Error while resetting database: {str(e)}"
            logger.error(error_msg)
            return Response(
                message=error_msg,
                status=False,
                data=None
            )
        finally:
            if self._init_lock.locked():
                self._init_lock.release()
                logger.info("Database reset lock released")

    def upsert(self, model, return_json: bool = True) -> Response:
        """Create or update an entity"""
        status = True
        model_class = type(model)
        existing_model = None

        with Session(self.engine) as session:
            try:
                if model.id:
                    existing_model = session.exec(select(model_class).where(model_class.id == model.id)).first()

                if existing_model:
                    model.updated_at = datetime.now()
                    for key, value in model.model_dump().items():
                        if key not in ['created_at', 'updated_at']:
                            # 确保config字段正确序列化
                            if key == 'config' and hasattr(value, 'model_dump'):
                                value = value.model_dump()
                            setattr(existing_model, key, value)
                    model = existing_model
                    session.add(model)
                else:
                    # 对于新记录，确保config字段正确序列化
                    if hasattr(model.config, 'model_dump'):
                        model.config = model.config.model_dump()
                    model.created_at = datetime.now()
                    model.updated_at = datetime.now()
                    session.add(model)

                session.commit()

                # 手动获取生成的ID
                if not existing_model and not model.id:
                    session.refresh(model)

            except Exception as e:
                session.rollback()
                logger.error("Error while updating/creating " + str(model_class.__name__) + ": " + str(e))
                status = False
                return Response(
                    message=f"Error creating/updating {model_class.__name__}: {str(e)}",
                    status=False,
                    data=None
                )

        message = (
            f"{model_class.__name__} Updated Successfully"
            if existing_model
            else f"{model_class.__name__} Created Successfully"
        )

        data = model.model_dump() if return_json else model

        return Response(
            message=message,
            status=status,
            data=data
        )

    @staticmethod
    def _model_to_dict(model_obj):
        return {col.name: getattr(model_obj, col.name) for col in model_obj.__table__.columns}

    def get(
            self,
            model_class,
            filters: dict | None = None,
            return_json: bool = False,
            order: str = "desc",
    ) -> Response:
        """List entities"""
        with Session(self.engine) as session:
            result = []
            status = True
            status_message = ""

            try:
                statement = select(model_class)  # type: ignore
                if filters:
                    conditions = [getattr(model_class, col) == value for col, value in filters.items()]
                    statement = statement.where(and_(*conditions))

                if hasattr(model_class, "created_at") and order:
                    order_by_clause = getattr(model_class.created_at, order)()  # Dynamically apply asc/desc
                    statement = statement.order_by(order_by_clause)

                items = session.exec(statement).all()
                result = [self._model_to_dict(item) if return_json else item for item in items]
                status_message = f"{model_class.__name__} Retrieved Successfully"
            except Exception as e:
                session.rollback()
                status = False
                status_message = f"Error while fetching {model_class.__name__}"
                logger.error("Error while getting items: " + str(model_class.__name__) + " " + str(e))

            return Response(
                message=status_message,
                status=status,
                data=result
            )

    def delete(self, model_class, filters: dict | None = None) -> Response:
        """Delete an entity"""
        status_message = ""
        status = True

        with Session(self.engine) as session:
            try:
                # Enable foreign key constraints for MySQL
                session.exec(text("SET FOREIGN_KEY_CHECKS = 1"))  # type: ignore

                statement = select(model_class)  # type: ignore
                if filters:
                    conditions = [getattr(model_class, col) == value for col, value in filters.items()]
                    statement = statement.where(and_(*conditions))

                rows = session.exec(statement).all()

                if rows:
                    for row in rows:
                        session.delete(row)
                    session.commit()
                    status_message = f"{model_class.__name__} Deleted Successfully"
                else:
                    status_message = "Row not found"
                    logger.info(f"Row with filters {filters} not found")

            except exc.IntegrityError as e:
                session.rollback()
                status = False
                status_message = f"Integrity error: The {model_class.__name__} is linked to another entity and cannot be deleted. {e}"
                # Log the specific integrity error
                logger.error(status_message)
            except Exception as e:
                session.rollback()
                status = False
                status_message = f"Error while deleting: {e}"
                logger.error(status_message)

        return Response(
            message=status_message,
            status=status,
            data=None
        )

    async def close(self) -> Response:
        """Close database connections and cleanup resources"""
        logger.info("Closing database connections...")
        try:
            # Dispose of the SQLAlchemy engine
            self.engine.dispose()
            logger.info("Database connections closed successfully")
            return Response(
                message="Database connections closed successfully",
                status=True,
                data=None
            )
        except Exception as e:
            error_msg = f"Error closing database connections: {str(e)}"
            logger.error(error_msg)
            return Response(
                message=error_msg,
                status=False,
                data=None
            )


_db_manager = None


def get_db(database_url) -> DatabaseManager:
    """Dependency to get database manager instance"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager(database_url)
    return _db_manager
