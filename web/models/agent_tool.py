from sqlalchemy import Column, Integer, String, Text, Boolean

from web.models.base import BaseModel


class ToolModel(BaseModel):
    __tablename__ = "tools"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), unique=True, nullable=False)
    description = Column(Text)
    icon = Column(String(512))
    tool_type = Column(String(50), nullable=False)
    config = Column(Text)  # 配置信息（JSON）
    is_enabled = Column(Boolean, default=True)  # 是否启用
    category = Column(String(100))  # 类型
