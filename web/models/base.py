from datetime import datetime
from typing import Optional

from sqlalchemy import Column, String, DateTime, func
from sqlmodel import SQLModel, Field

from web.db.database import Base as _Base


class BaseModel(_Base):
    __abstract__ = True

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False
    )
    created_by = Column(String(255), nullable=True)


class BaseDBModel(SQLModel, table=False):

    __abstract__ = True

    id: Optional[int] = Field(default=None, primary_key=True)

    created_at: datetime = Field(
        default_factory=datetime.now,
        sa_type=DateTime(timezone=False),
        sa_column_kwargs={"server_default": func.now(), "nullable": True},
    )

    updated_at: datetime = Field(
        default_factory=datetime.now,
        sa_type=DateTime(timezone=False), 
        sa_column_kwargs={"onupdate": func.now(), "nullable": True},
    )

    user_id: Optional[str] = None
    version: Optional[str] = "0.0.1"
