from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException
from web.models import DatabaseManager,Response,AgentsConfig, get_db

router = APIRouter()


@router.put("/{agent_id}")
async def update_agent_entry(
    agent_id: int, agent_data: AgentsConfig, user_id: str, db: DatabaseManager = Depends(get_db)
) -> Response:
    # Check ownership first
    result = db.get(AgentsConfig, filters={"id": agent_id})
    if not result.status or not result.data:
        raise HTTPException(status_code=404, detail="AgentsConfig entry not found")

    if result.data[0].user_id != user_id:
        raise HTTPException(status_code=403, detail="Not authorized to update this agent entry")

    # Update if authorized
    agent_data.id = agent_id  # Ensure ID matches
    agent_data.user_id = user_id  # Ensure user_id matches
    return db.upsert(agent_data)


@router.post("/")
async def create_agent_entry(agent_data: AgentsConfig, db: DatabaseManager = Depends(get_db)) -> Response:
    response = db.upsert(agent_data)
    if not response.status:
        raise HTTPException(status_code=400, detail=response.message)
    return response


@router.get("/")
async def list_agent_entries(user_id: str, db: DatabaseManager = Depends(get_db)) -> Response:
    try:
        result = db.get(AgentsConfig, filters={"user_id": user_id})
        if not result.data or len(result.data) == 0:
            # create a default agent entry
            # agent_config = create_default_agent()
            # default_agent = AgentsConfig(user_id=user_id, config=agent_config.model_dump())
            # db.upsert(default_agent)
            result = db.get(AgentsConfig, filters={"user_id": user_id})
        return result
    except Exception as e:
        return Response(status=False, data=[], message=f"Error retrieving agent entries: {str(e)}")


@router.get("/{agent_id}")
async def get_agent_entry(agent_id: int, user_id: str, db: DatabaseManager = Depends(get_db)) -> Response:
    result = db.get(AgentsConfig, filters={"id": agent_id, "user_id": user_id})
    if not result.status or not result.data:
        raise HTTPException(status_code=404, detail="AgentsConfig entry not found")

    return Response(status=result.status, data=result.data[0], message=result.message)


@router.delete("/{agent_id}")
async def delete_agent_entry(agent_id: int, user_id: str, db: DatabaseManager = Depends(get_db)) -> Response:
    # Check ownership first
    result = db.get(AgentsConfig, filters={"id": agent_id, "user_id": user_id})

    if not result.status or not result.data:
        raise HTTPException(status_code=404, detail="AgentsConfig entry not found")
    response = db.delete(AgentsConfig, filters={"id": agent_id})
    # Delete if authorized
    return response
