from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException
from web.models import DatabaseManager,Response,ToolsConfig, get_db

router = APIRouter()


@router.put("/{tool_id}")
async def update_tool_entry(
    tool_id: int, tool_data: ToolsConfig, user_id: str, db: DatabaseManager = Depends(get_db)
) -> Response:
    # Check ownership first
    result = db.get(ToolsConfig, filters={"id": tool_id})
    if not result.status or not result.data:
        raise HTTPException(status_code=404, detail="ToolsConfig entry not found")

    if result.data[0].user_id != user_id:
        raise HTTPException(status_code=403, detail="Not authorized to update this tool entry")

    # Update if authorized
    tool_data.id = tool_id  # Ensure ID matches
    tool_data.user_id = user_id  # Ensure user_id matches
    return db.upsert(tool_data)


@router.post("/")
async def create_tool_entry(tool_data: ToolsConfig, db: DatabaseManager = Depends(get_db)) -> Response:
    response = db.upsert(tool_data)
    if not response.status:
        raise HTTPException(status_code=400, detail=response.message)
    return response


@router.get("/")
async def list_tool_entries(user_id: str, db: DatabaseManager = Depends(get_db)) -> Response:
    try:
        result = db.get(ToolsConfig, filters={"user_id": user_id})
        if not result.data or len(result.data) == 0:
            # create a default tool entry
            # tool_config = create_default_tool()
            # default_tool = ToolsConfig(user_id=user_id, config=tool_config.model_dump())
            # db.upsert(default_tool)
            result = db.get(ToolsConfig, filters={"user_id": user_id})
        return result
    except Exception as e:
        return Response(status=False, data=[], message=f"Error retrieving tool entries: {str(e)}")


@router.get("/{tool_id}")
async def get_tool_entry(tool_id: int, user_id: str, db: DatabaseManager = Depends(get_db)) -> Response:
    result = db.get(ToolsConfig, filters={"id": tool_id, "user_id": user_id})
    if not result.status or not result.data:
        raise HTTPException(status_code=404, detail="ToolsConfig entry not found")

    return Response(status=result.status, data=result.data[0], message=result.message)


@router.delete("/{tool_id}")
async def delete_tool_entry(tool_id: int, user_id: str, db: DatabaseManager = Depends(get_db)) -> Response:
    # Check ownership first
    result = db.get(ToolsConfig, filters={"id": tool_id, "user_id": user_id})

    if not result.status or not result.data:
        raise HTTPException(status_code=404, detail="ToolsConfig entry not found")
    response = db.delete(ToolsConfig, filters={"id": tool_id})
    # Delete if authorized
    return response
