from datetime import datetime
from typing import Optional, Dict

from fastapi import HTTPException, Depends, APIRouter, Query
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from web.api.v1.endpoints.response import ApiResponse, CreateAgentResponseData
from web.core.logger import logging
from web.db.database import engine, Base, get_async_db
from web.models.agent_basic import AgentModel
from web.services.agent_basic import AgentBasicService

basic_router = APIRouter()  # 创建路由对象

Base.metadata.create_all(bind=engine)  # 创建所有表


def serialize_config(config_dict):
    """递归处理 SecretStr 字段"""
    if isinstance(config_dict, dict):
        return {k: serialize_config(v) for k, v in config_dict.items()}
    elif isinstance(config_dict, list):
        return [serialize_config(item) for item in config_dict]
    elif hasattr(config_dict, "get_secret_value"):
        return config_dict.get_secret_value()  # ← 获取真实字符串
    else:
        return config_dict


# agent 创建请求
class AgentRequest(BaseModel):
    name: str  # 必选 string
    icon: Optional[str] = None  # 可选 string，图标
    desc: Optional[str] = None  # 可选 string，描述
    created_by: Optional[str] = None  # 如用户ID、用户名 user_id version


# agent 更新请求
class UpdateAgentRequest(BaseModel):
    agent_id: str
    name: Optional[str] = None
    icon: Optional[str] = None
    desc: Optional[str] = None
    config_dict: Optional[str] = None  # 可加可不加，先加上


# agent 详细信息返回
class AgentDetailDataResponse(BaseModel):
    id: int
    name: str
    desc: Optional[str] = None
    icon: str
    config_dict: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None

    class Config:
        orm_mode = True


@basic_router.post("/create", response_model=ApiResponse[CreateAgentResponseData], status_code=201)
async def create_agent(agent_in: AgentRequest, db: AsyncSession = Depends(get_async_db)):
    logging.info(f"创建 Agent 请求，参数: {agent_in}")

    # 创建数据库对象
    db_agent = AgentModel(
        name=agent_in.name,
        desc=agent_in.desc,
        icon=agent_in.icon,
        # config_dict=config_json,
        created_by=agent_in.created_by
    )

    try:
        saved_agent = await AgentBasicService.add_agent_basic(db, db_agent)
        logging.info(f"Agent 创建成功，ID: {saved_agent.id}")
    except ValueError as e:
        logging.exception(f"创建失败：{str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except RuntimeError as e:
        logging.exception(f"创建 Agent 失败：{str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

    return ApiResponse[CreateAgentResponseData](
        code=200,
        msg="创建成功",
        data=CreateAgentResponseData(agentId=str(saved_agent.id))
    )


@basic_router.get("/counts", response_model=ApiResponse[int])
async def get_agent_counts(db: AsyncSession = Depends(get_async_db)):
    count = await AgentBasicService.count_agents(db)
    return ApiResponse(code=200, msg="获取成功", data=count)


@basic_router.put("/update", response_model=ApiResponse[AgentDetailDataResponse])
async def update_agent(request: UpdateAgentRequest, db: AsyncSession = Depends(get_async_db)):
    ''' 更新（全部更新，不管是否有变化） '''
    logging.info(f"更新 Agent 请求: {request}")
    try:
        updated_agent, is_updated = await AgentBasicService.update_agent_basic(
            sess=db,
            agent_id=request.agent_id,
            name=request.name,
            icon=request.icon,
            desc=request.desc,
            config_dict=request.config_dict
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except RuntimeError as e:
        raise HTTPException(status_code=500, detail=str(e))

    # 构造响应数据
    response_data = AgentDetailDataResponse(
        id=updated_agent.id,
        name=updated_agent.name,
        desc=updated_agent.desc,
        icon=updated_agent.icon,
        config_dict=updated_agent.config_dict,
        created_at=updated_agent.created_at,
        updated_at=updated_agent.updated_at,
        created_by=updated_agent.created_by
    )

    msg = "更新成功" if is_updated else "没有需要更新的参数"

    return ApiResponse(
        code=200,
        msg=msg,
        data=response_data
    )


@basic_router.delete("/delete/{agent_id}", response_model=ApiResponse[dict])
async def delete_agent(agent_id: int, db: AsyncSession = Depends(get_async_db)):
    ''' 根据id删除 '''
    success = await AgentBasicService.remove_agent_basic_by_id(db, agent_id)
    if not success:
        raise HTTPException(status_code=404, detail="Agent 不存在或已被删除")

    return ApiResponse(code=200, msg="删除成功", data={})


@basic_router.get("/read/{agent_id}", response_model=ApiResponse[AgentDetailDataResponse])
async def read_agent(agent_id: int, db: AsyncSession = Depends(get_async_db)):
    '''
    根据 agent_id 查询 agent 信息
    Args:
        agent_id:
        db:

    Returns:

    '''
    logging.info(f"查询 Agent 请求，参数: {agent_id}")
    agent = await AgentBasicService.get_agent_basic_by_id(db, agent_id)
    if not agent:
        logging.warning(f"查询失败，Agent {agent_id} 未找到！ ")
        raise HTTPException(status_code=404, detail="Agent 未找到")

    return ApiResponse[AgentDetailDataResponse](
        code=200,
        msg="查询成功",
        data=AgentDetailDataResponse(
            id=agent.id,
            name=agent.name,
            desc=agent.desc,
            icon=agent.icon,
            config_dict=agent.config_dict,
            created_at=agent.created_at,
            updated_at=agent.updated_at,
            created_by=agent.created_by,
        )
    )


@basic_router.get("/list_agents", response_model=ApiResponse[Dict[str, list]])
async def list_agent_ids(
        from_: int = Query(0, description="起始位置，从0开始", ge=0),
        size: int = Query(10, description="每页数量", ge=1, le=100),
        db: AsyncSession = Depends(get_async_db)
):
    """ 分页获取 Agent ID 列表 """
    logging.info(f"分页查询 Agent IDs: from={from_}, size={size}")

    try:
        data = await AgentBasicService.get_agent_ids_paginated(db, from_=from_, size=size)
    except RuntimeError as e:
        logging.exception(f"分页查询失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

    return ApiResponse(code=200, msg="获取成功", data=data)
