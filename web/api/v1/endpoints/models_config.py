from fastapi import <PERSON>Router, Depends, HTTPException
from web.models import DatabaseManager,Response,ModelsConfig, get_db
router = APIRouter()


@router.put("/{model_id}")
async def update_model_entry(
    model_id: int, model_data: ModelsConfig, user_id: str, db: DatabaseManager = Depends(get_db)
) -> Response:
    # Check ownership first
    result = db.get(ModelsConfig, filters={"id": model_id})
    if not result.status or not result.data:
        raise HTTPException(status_code=404, detail="ModelsConfig entry not found")

    if result.data[0].user_id != user_id:
        raise HTTPException(status_code=403, detail="Not authorized to update this model entry")

    # Update if authorized
    model_data.id = model_id  # Ensure ID matches
    model_data.user_id = user_id  # Ensure user_id matches
    return db.upsert(model_data)


@router.post("/")
async def create_model_entry(model_data: ModelsConfig, db: DatabaseManager = Depends(get_db)) -> Response:
    try:
        response = db.upsert(model_data)
        if not response.status:
            raise HTTPException(status_code=422, detail=response.message)  # 改为422
        return response
    except Exception as e:
        raise HTTPException(status_code=422, detail=str(e))  # 改为422


@router.get("/")
async def list_model_entries(user_id: str, db: DatabaseManager = Depends(get_db)) -> Response:
    try:
        result = db.get(ModelsConfig, filters={"user_id": user_id})
        if not result.data or len(result.data) == 0:
            # create a default model entry
            # model_config = create_default_model()
            # default_model = ModelsConfig(user_id=user_id, config=model_config.model_dump())
            # db.upsert(default_model)
            result = db.get(ModelsConfig, filters={"user_id": user_id})
        return result
    except Exception as e:
        return Response(status=False, data=[], message=f"Error retrieving model entries: {str(e)}")


@router.get("/{model_id}")
async def get_model_entry(model_id: int, user_id: str, db: DatabaseManager = Depends(get_db)) -> Response:
    result = db.get(ModelsConfig, filters={"id": model_id, "user_id": user_id})
    if not result.status or not result.data:
        raise HTTPException(status_code=404, detail="ModelsConfig entry not found")

    return Response(status=result.status, data=result.data[0], message=result.message)


@router.delete("/{model_id}")
async def delete_model_entry(model_id: int, user_id: str, db: DatabaseManager = Depends(get_db)) -> Response:
    # Check ownership first
    result = db.get(ModelsConfig, filters={"id": model_id, "user_id": user_id})

    if not result.status or not result.data:
        raise HTTPException(status_code=404, detail="ModelsConfig entry not found")
    response = db.delete(ModelsConfig, filters={"id": model_id})
    # Delete if authorized
    return response
