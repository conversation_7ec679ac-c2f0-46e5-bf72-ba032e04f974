import sys

from loguru import logger

# 移除默认的 handler
logger.remove()

logger.add(
    sink=sys.stdout,
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
    level="INFO",
    serialize=False  # 开发环境可设为 False，生产可设为 True 输出 JSON
)

logger.add(
    "logs/app_{time:YYYY-MM-DD}.log",
    rotation="00:00",  # 每天生成一个新文件
    retention="7 days",
    level="INFO",
    encoding="utf-8"
)

# 导出 logger
logging = logger

'''
# 使用示例
logging.info("用户登录成功", user_id=123)
logging.debug("调试信息：当前状态为 {}", "active")
logging.warning("配置文件缺失，使用默认值")
logging.error("数据库连接失败")
logging.critical("系统即将关闭")
logging.success("操作执行成功")  # loguru 特有
logging.exception("捕获到未处理异常")  # 用于异常捕获上下文
'''
