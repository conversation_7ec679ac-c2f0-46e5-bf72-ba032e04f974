import traceback

from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse

from web.core.logger import logger


async def global_exception_handler(request: Request, exc: Exception):
    """
    全局异常处理器：捕获所有未处理的异常
    """
    # 获取请求信息
    method = request.method
    url = str(request.url)
    client_ip = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "unknown")

    status_code = 500
    msg = "服务器内部错误"

    if isinstance(exc, HTTPException):
        status_code = exc.status_code
        msg = exc.detail
        logger.warning(
            "Client Error: {method} {url} | Status: {status} | IP: {ip} | Msg: {msg}",
            method=method,
            url=url,
            status=status_code,
            ip=client_ip,
            msg=msg
        )
    else:
        logger.error(
            "Server Error: {method} {url} | IP: {ip} | User-Agent: {ua} | Exception: {exc}\n{traceback}",
            method=method,
            url=url,
            ip=client_ip,
            ua=user_agent,
            exc=str(exc),
            traceback=traceback.format_exc()
        )

    return JSONResponse(
        content={
            "code": status_code,
            "msg": msg,
            "data": None
        },
        status_code=status_code
    )
