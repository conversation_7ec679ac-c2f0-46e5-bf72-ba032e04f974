import contextlib
import os
from typing import Dict, <PERSON><PERSON>, Iterator, Optional

from opentelemetry.trace import NoOpTracerProvider, Span, SpanKind, TracerProvider, get_tracer_provider
from opentelemetry.util import types

from ._propagation import TelemetryMetadataContainer, get_telemetry_links
from ._tracing_config import Destination, ExtraAttributes, Operation, TracingConfig


class TraceHelper(Generic[Operation, Destination, ExtraAttributes]):

    def __init__(
        self,
        tracer_provider: TracerProvider | None,
        instrumentation_builder_config: TracingConfig[Operation, Destination, ExtraAttributes],
    ) -> None:
        self.instrumentation_builder_config = instrumentation_builder_config

        disable_runtime_tracing = os.environ.get("AGEN_DISABLE_RUNTIME_TRACING") == "true"
        if disable_runtime_tracing:
            self.tracer_provider: TracerProvider = NoOpTracerProvider()
            self.tracer = self.tracer_provider.get_tracer(f"autogen {instrumentation_builder_config.name}")
            return

        self.tracer_provider = tracer_provider or get_tracer_provider() or NoOpTracerProvider()
        self.tracer = self.tracer_provider.get_tracer(f"autogen {instrumentation_builder_config.name}")

    @contextlib.contextmanager
    def trace_block(
        self,
        operation: Operation,
        destination: Destination,
        parent: Optional[TelemetryMetadataContainer],
        *,
        extraAttributes: ExtraAttributes | None = None,
        kind: Optional[SpanKind] = None,
        attributes: Optional[types.Attributes] = None,
        start_time: Optional[int] = None,
        record_exception: bool = True,
        set_status_on_exception: bool = True,
        end_on_exit: bool = True,
    ) -> Iterator[Span]:

        span_name = self.instrumentation_builder_config.get_span_name(operation, destination)
        span_kind = kind or self.instrumentation_builder_config.get_span_kind(operation)
        context = None
        links = get_telemetry_links(parent) if parent else None
        attributes_with_defaults: Dict[str, types.AttributeValue] = {}
        for key, value in (attributes or {}).items():
            attributes_with_defaults[key] = value
        instrumentation_attributes = self.instrumentation_builder_config.build_attributes(
            operation, destination, extraAttributes
        )
        for key, value in instrumentation_attributes.items():
            attributes_with_defaults[key] = value
        with self.tracer.start_as_current_span(
            span_name,
            context,
            span_kind,
            attributes_with_defaults,
            links,
            start_time,
            record_exception,
            set_status_on_exception,
            end_on_exit,
        ) as span:
            yield span
