from __future__ import annotations

from typing import Awaitable, Callable, Protocol, runtime_checkable

from ._agent_id import AgentId
from ._topic import TopicId


@runtime_checkable
class Subscription(Protocol):

    @property
    def id(self) -> str:
        ...

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, Subscription):
            return False

        return self.id == other.id

    def is_match(self, topic_id: TopicId) -> bool:
        ...

    def map_to_agent(self, topic_id: TopicId) -> AgentId:
        ...


UnboundSubscription = Callable[[], list[Subscription] | Awaitable[list[Subscription]]]
