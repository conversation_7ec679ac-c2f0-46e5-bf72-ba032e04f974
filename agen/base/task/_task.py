from typing import AsyncGenerator, Protocol, Sequence

from agen.base import Can<PERSON>ation<PERSON>oken
from pydantic import BaseModel

from agen.messages import BaseAgentEvent, BaseChatMessage


class TaskResult(BaseModel):

    messages: Sequence[BaseAgentEvent | BaseChatMessage]

    stop_reason: str | None = None


class TaskRunner(Protocol):

    async def run(
        self,
        *,
        task: str | BaseChatMessage | Sequence[BaseChatMessage] | None = None,
        cancellation_token: CancellationToken | None = None,
        output_task_messages: bool = True,
    ) -> TaskResult:
        ...

    def run_stream(
        self,
        *,
        task: str | BaseChatMessage | Sequence[BaseChatMessage] | None = None,
        cancellation_token: CancellationToken | None = None,
        output_task_messages: bool = True,
    ) -> AsyncGenerator[BaseAgentEvent | BaseChatMessage | TaskResult, None]:
        ...
