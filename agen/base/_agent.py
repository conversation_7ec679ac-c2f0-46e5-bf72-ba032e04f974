from typing import TYPE_CHECKING, Any, Mapping, Protocol, runtime_checkable

from ._agent_id import AgentId
from ._agent_metadata import AgentMetadata
from ._message_context import MessageContext

if TYPE_CHECKING:
    from ._agent_runtime import AgentRuntime


@runtime_checkable
class Agent(Protocol):
    @property
    def metadata(self) -> AgentMetadata:
        ...

    @property
    def id(self) -> AgentId:
        ...

    async def bind_id_and_runtime(self, id: AgentId, runtime: "AgentRuntime") -> None:
        ...

    async def on_message(self, message: Any, ctx: MessageContext) -> Any:
        ...

    async def save_state(self) -> Mapping[str, Any]:
        ...

    async def load_state(self, state: Mapping[str, Any]) -> None:
        ...

    async def close(self) -> None:
        ...
