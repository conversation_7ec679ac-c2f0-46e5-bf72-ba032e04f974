
from pydantic import BaseModel
from ._cache_store import CacheStore
from agen.base.component import Component
from typing import TypeV<PERSON>,Dict,Optional
from typing_extensions import Self
T = TypeVar("T")

class InMemoryStoreConfig(BaseModel):
    pass


class InMemoryStore(CacheStore[T], Component[InMemoryStoreConfig]):
    component_provider_override = "agen.cache_store.InMemoryStore"
    component_config_schema = InMemoryStoreConfig

    def __init__(self) -> None:
        self.store: Dict[str, T] = {}

    def get(self, key: str, default: Optional[T] = None) -> Optional[T]:
        return self.store.get(key, default)

    def set(self, key: str, value: T) -> None:
        self.store[key] = value

    def _to_config(self) -> InMemoryStoreConfig:
        return InMemoryStoreConfig()

    @classmethod
    def _from_config(cls, config: InMemoryStoreConfig) -> Self:
        return cls()