from abc import ABC, abstractmethod
from typing import  Generic, Optional, TypeVar

from pydantic import BaseModel

from agen.base.component import ComponentBase

T = TypeVar("T")


class CacheStore(ABC, Generic[T], ComponentBase[BaseModel]):


    component_type = "cache_store"

    @abstractmethod
    def get(self, key: str, default: Optional[T] = None) -> Optional[T]:
        ...

    @abstractmethod
    def set(self, key: str, value: T) -> None:
        ...

