
from typing import List, Literal, Optional, Union

from pydantic import BaseModel, Field
from typing_extensions import Annotated

from agen.utils import Image

from dataclasses import dataclass
@dataclass
class FunctionCall:
    id: str
    # JSON args
    arguments: str
    # Function to call
    name: str

class SystemMessage(BaseModel):
    content: str
    type: Literal["SystemMessage"] = "SystemMessage"


class UserMessage(BaseModel):
    content: Union[str, List[Union[str, Image]]]
    source: str
    type: Literal["UserMessage"] = "UserMessage"


class AssistantMessage(BaseModel):
    content: Union[str, List[FunctionCall]]
    thought: str | None = None
    source: str
    type: Literal["AssistantMessage"] = "AssistantMessage"


class FunctionExecutionResult(BaseModel):
    content: str
    name: str
    call_id: str
    is_error: bool | None = None


class FunctionExecutionResultMessage(BaseModel):
    content: List[FunctionExecutionResult]
    type: Literal["FunctionExecutionResultMessage"] = "FunctionExecutionResultMessage"


LLMMessage = Annotated[
    Union[SystemMessage, UserMessage, AssistantMessage, FunctionExecutionResultMessage], Field(discriminator="type")
]


@dataclass
class RequestUsage:
    prompt_tokens: int
    completion_tokens: int


FinishReasons = Literal["stop", "length", "function_calls", "content_filter", "unknown"]


@dataclass
class TopLogprob:
    logprob: float
    bytes: Optional[List[int]] = None


class ChatCompletionTokenLogprob(BaseModel):
    token: str
    logprob: float
    top_logprobs: Optional[List[TopLogprob] | None] = None
    bytes: Optional[List[int]] = None


class CreateResult(BaseModel):
    finish_reason: FinishReasons
    content: Union[str, List[FunctionCall]]
    usage: RequestUsage
    cached: bool
    logprobs: Optional[List[ChatCompletionTokenLogprob] | None] = None
    thought: Optional[str] = None
