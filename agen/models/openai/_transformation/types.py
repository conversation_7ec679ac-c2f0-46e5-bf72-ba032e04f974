from typing import Any, Callable, Dict, List, Sequence, Type, Union

from agen.models import FunctionCall
from agen.utils import Image
from agen.models import LLMMessage,FunctionExecutionResult
from openai.types.chat import ChatCompletionMessageParam

MessageParam = Union[ChatCompletionMessageParam]
TrasformerReturnType = Sequence[MessageParam]
TransformerFunc = Callable[[LLMMessage, Dict[str, Any]], TrasformerReturnType]
TransformerMap = Dict[Type[LLMMessage], TransformerFunc]

LLMMessageContent = Union[
    str,
    List[Union[str, Image]],
    List[FunctionCall],
    List[FunctionExecutionResult],
]
