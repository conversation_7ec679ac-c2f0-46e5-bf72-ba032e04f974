from collections import defaultdict
from typing import Any, Callable, Dict, List, get_args

from agen.models import LLMMessage, ModelFamily

from .types import (
    TransformerFunc,
    TransformerMap,
)

MESSAGE_TRANSFORMERS: Dict[str, Dict[str, TransformerMap]] = defaultdict(dict)


def build_transformer_func(
    funcs: List[Callable[[LLMMessage, Dict[str, Any]], Dict[str, Any]]], message_param_func: Callable[..., Any]
) -> TransformerFunc:

    def transformer_func(message: LLMMessage, context: Any) -> Any:
        kwargs: Dict[str, Any] = {}
        for func in funcs:
            kwargs.update(func(message, context))
        return [message_param_func(**kwargs)]

    return transformer_func


def build_conditional_transformer_func(
    funcs_map: Dict[str, List[Callable[[LLMMessage, Dict[str, Any]], Dict[str, Any]]]],
    message_param_func_map: Dict[str, Callable[..., Any]],
    condition_func: Callable[[LLMMessage, Dict[str, Any]], str],
) -> TransformerFunc:

    def transformer(message: LLMMessage, context: Dict[str, Any]) -> Any:
        condition = condition_func(message, context)
        message_param_func = message_param_func_map[condition]
        kwargs: Dict[str, Any] = {}
        for func in funcs_map[condition]:
            kwargs.update(func(message, context))
        if kwargs.get("pass_message", False):
            return []
        return [message_param_func(**kwargs)]

    return transformer


def register_transformer(api: str, model_family: str, transformer_map: TransformerMap) -> None:

    MESSAGE_TRANSFORMERS[api][model_family] = transformer_map


def _find_model_family(api: str, model: str) -> str:

    len_family = 0
    family = ModelFamily.UNKNOWN
    for _family in MESSAGE_TRANSFORMERS[api].keys():
        if model.startswith(_family):
            if len(_family) > len_family:
                family = _family
                len_family = len(_family)
    return family


def get_transformer(api: str, model: str, model_family: str) -> TransformerMap:


    if model_family not in set(get_args(ModelFamily.ANY)) or model_family == ModelFamily.UNKNOWN:
        model_family = _find_model_family(api, model)

    transformer = MESSAGE_TRANSFORMERS.get(api, {}).get(model_family, {})

    if not transformer:
        raise ValueError(f"No transformer found for model family '{model_family}'")

    return transformer
