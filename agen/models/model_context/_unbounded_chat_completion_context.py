from typing import List

from pydantic import BaseModel
from typing_extensions import Self

from agen.base.component import Component
from agen.models import LLMMessage
from ._chat_completion_context import ChatCompletionContext


class UnboundedChatCompletionContextConfig(BaseModel):
    initial_messages: List[LLMMessage] | None = None


class UnboundedChatCompletionContext(ChatCompletionContext, Component[UnboundedChatCompletionContextConfig]):

    component_config_schema = UnboundedChatCompletionContextConfig
    component_provider_override = "agen.models.model_context.UnboundedChatCompletionContext"

    async def get_messages(self) -> List[LLMMessage]:
        return self._messages

    def _to_config(self) -> UnboundedChatCompletionContextConfig:
        return UnboundedChatCompletionContextConfig(initial_messages=self._initial_messages)

    @classmethod
    def _from_config(cls, config: UnboundedChatCompletionContextConfig) -> Self:
        return cls(initial_messages=config.initial_messages)
