import uuid
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from typing import  Dict

from agen.models import (
    RequestUsage,
)

from pydantic import  Field
from ._message import Message

class BaseAgentEvent(Message, ABC):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    source: str
    models_usage: RequestUsage | None = None
    metadata: Dict[str, str] = {}
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))