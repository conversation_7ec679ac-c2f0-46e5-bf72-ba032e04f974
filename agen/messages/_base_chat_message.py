import uuid
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from typing import  Dict

from agen.models import (
    RequestUsage,
    UserMessage,
)

from pydantic import  Field
from ._message import Message


class BaseChatMessage(Message, ABC):

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    source: str
    models_usage: RequestUsage | None = None
    metadata: Dict[str, str] = {}
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


    @abstractmethod
    def to_model_text(self) -> str:
        ...

    @abstractmethod
    def to_model_message(self) -> UserMessage:
        ...