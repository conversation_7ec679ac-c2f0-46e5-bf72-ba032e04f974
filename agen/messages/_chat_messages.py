

from abc import ABC
from typing import  List, Literal



from agen.models import  FunctionCall
from agen.utils import Image


from agen.models import (
    FunctionExecutionResult,
    LLMMessage,
    UserMessage,
)
from ._base_chat_message import BaseChatMessage


class BaseTextChatMessage(BaseChatMessage, ABC):
    content: str

    def to_text(self) -> str:
        return self.content

    def to_model_text(self) -> str:
        return self.content

    def to_model_message(self) -> UserMessage:
        return UserMessage(content=self.content, source=self.source)

class TextMessage(BaseTextChatMessage):
    type: Literal["TextMessage"] = "TextMessage"

class MultiModalMessage(BaseChatMessage):
    content: List[str | Image]
    type: Literal["MultiModalMessage"] = "MultiModalMessage"

    def to_model_text(self, image_placeholder: str | None = "[image]") -> str:
        text = ""
        for c in self.content:
            if isinstance(c, str):
                text += c
            elif isinstance(c, Image):
                if image_placeholder is not None:
                    text += f" {image_placeholder}"
                else:
                    text += f" {c.to_base64()}"
        return text

    def to_text(self, iterm: bool = False) -> str:
        result: List[str] = []
        for c in self.content:
            if isinstance(c, str):
                result.append(c)
            else:
                if iterm:
                    image_data = c.to_base64()
                    result.append(f"\033]1337;File=inline=1:{image_data}\a\n")
                else:
                    result.append("<image>")
        return "\n".join(result)

    def to_model_message(self) -> UserMessage:
        return UserMessage(content=self.content, source=self.source)

class StopMessage(BaseTextChatMessage):
    type: Literal["StopMessage"] = "StopMessage"

class HandoffMessage(BaseTextChatMessage):

    target: str
    context: List[LLMMessage] = []
    type: Literal["HandoffMessage"] = "HandoffMessage"

class ToolCallSummaryMessage(BaseTextChatMessage):
    type: Literal["ToolCallSummaryMessage"] = "ToolCallSummaryMessage"
    tool_calls: List[FunctionCall]
    results: List[FunctionExecutionResult]
