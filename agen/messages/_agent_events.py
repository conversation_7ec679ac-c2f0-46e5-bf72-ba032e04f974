from._base_agent_event import Base<PERSON>gentEvent
from typing import List, Literal

from agen.models import Fun<PERSON><PERSON>all,FunctionExecutionResult
from agen.tools.code_executor import CodeBlock, CodeResult
from agen.memory import MemoryContent

class ToolCallRequestEvent(BaseAgentEvent):
    content: List[FunctionCall]
    type: Literal["ToolCallRequestEvent"] = "ToolCallRequestEvent"

    def to_text(self) -> str:
        return str(self.content)

class ToolCallExecutionEvent(BaseAgentEvent):

    content: List[FunctionExecutionResult]
    type: Literal["ToolCallExecutionEvent"] = "ToolCallExecutionEvent"

    def to_text(self) -> str:
        return str(self.content)


class CodeGenerationEvent(BaseAgentEvent):
    retry_attempt: int
    content: str
    code_blocks: List[CodeBlock]
    type: Literal["CodeGenerationEvent"] = "CodeGenerationEvent"

    def to_text(self) -> str:
        return self.content


class CodeExecutionEvent(BaseAgentEvent):
    retry_attempt: int
    result: CodeResult
    type: Literal["CodeExecutionEvent"] = "CodeExecutionEvent"

    def to_text(self) -> str:
        return self.result.output



class UserInputRequestedEvent(BaseAgentEvent):
    request_id: str
    content: Literal[""] = ""
    type: Literal["UserInputRequestedEvent"] = "UserInputRequestedEvent"

    def to_text(self) -> str:
        return str(self.content)


class MemoryQueryEvent(BaseAgentEvent):

    content: List[MemoryContent]
    type: Literal["MemoryQueryEvent"] = "MemoryQueryEvent"

    def to_text(self) -> str:
        return str(self.content)


class ModelClientStreamingChunkEvent(BaseAgentEvent):

    content: str
    full_message_id: str | None = None
    type: Literal["ModelClientStreamingChunkEvent"] = "ModelClientStreamingChunkEvent"

    def to_text(self) -> str:
        return self.content


class ThoughtEvent(BaseAgentEvent):

    content: str # 模型的思考过程
    type: Literal["ThoughtEvent"] = "ThoughtEvent"

    def to_text(self) -> str:
        return self.content


class SelectSpeakerEvent(BaseAgentEvent):

    content: List[str]
    type: Literal["SelectSpeakerEvent"] = "SelectSpeakerEvent"

    def to_text(self) -> str:
        return str(self.content)


class SelectorEvent(BaseAgentEvent):
    content: str
    type: Literal["SelectorEvent"] = "SelectorEvent"

    def to_text(self) -> str:
        return str(self.content)
