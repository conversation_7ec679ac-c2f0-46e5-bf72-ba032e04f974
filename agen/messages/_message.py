from abc import ABC, abstractmethod
from typing import Any, Mapping
from pydantic import BaseModel
from typing_extensions import Self


class Message(BaseModel, ABC):
    @abstractmethod
    def to_text(self) -> str:
        ...

    def dump(self) -> Mapping[str, Any]:
        return self.model_dump()

    @classmethod
    def load(cls, data: Mapping[str, Any]) -> Self:
        return cls.model_validate(data)