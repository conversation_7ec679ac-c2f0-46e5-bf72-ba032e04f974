from abc import ABC, abstractmethod
from types import TracebackType
from typing import Any, AsyncGenerator, List, Literal, Mapping, Optional, Type

from pydantic import BaseModel, Field
from typing_extensions import Annotated, Self

from agen.base import CancellationToken
from agen.base.component import ComponentBase
from agen.utils import Image
from ._tool import ToolSchema




class TextResultContent(BaseModel):

    type: Literal["TextResultContent"] = "TextResultContent"
    content: str


class ImageResultContent(BaseModel):
    type: Literal["ImageResultContent"] = "ImageResultContent"
    content: Image


ResultContent = Annotated[TextResultContent | ImageResultContent, Field(discriminator="type")]


class ToolResult(BaseModel):
    type: Literal["ToolResult"] = "ToolResult"
    name: str
    result: List[ResultContent]
    is_error: bool = False

    def to_text(self, replace_image: str | None = None) -> str:

        parts: List[str] = []
        for content in self.result:
            if isinstance(content, TextResultContent):
                parts.append(content.content)
            elif isinstance(content, ImageResultContent):
                if replace_image is not None:
                    parts.append(replace_image)
                else:
                    parts.append(f"[Image: {content.content.to_base64()}]")
        return "\n".join(parts)


class Workbench(ABC, ComponentBase[BaseModel]):

    component_type = "workbench"

    @abstractmethod
    async def list_tools(self) -> List[ToolSchema]:
        ...

    @abstractmethod
    async def call_tool(
        self,
        name: str,
        arguments: Mapping[str, Any] | None = None,
        cancellation_token: CancellationToken | None = None,
        call_id: str | None = None,
    ) -> ToolResult:
        ...

    @abstractmethod
    async def start(self) -> None:
        ...

    @abstractmethod
    async def stop(self) -> None:
        ...

    @abstractmethod
    async def reset(self) -> None:
        ...

    @abstractmethod
    async def save_state(self) -> Mapping[str, Any]:
        ...

    @abstractmethod
    async def load_state(self, state: Mapping[str, Any]) -> None:
        ...

    async def __aenter__(self) -> Self:
        await self.start()
        return self

    async def __aexit__(
        self, exc_type: Optional[Type[BaseException]], exc_val: Optional[BaseException], exc_tb: Optional[TracebackType]
    ) -> None:
        await self.stop()


class StreamWorkbench(Workbench, ABC):
    @abstractmethod
    def call_tool_stream(
        self,
        name: str,
        arguments: Mapping[str, Any] | None = None,
        cancellation_token: CancellationToken | None = None,
        call_id: str | None = None,
    ) -> AsyncGenerator[Any | ToolResult, None]:
        ...