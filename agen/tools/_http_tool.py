import re
from typing import Any, Literal, Optional, Type

import httpx
# todo 安装
from json_schema_to_pydantic import create_model
from pydantic import BaseModel, Field
from typing_extensions import Self

from agen.base import CancellationToken
from agen.base.component import Component
from ._tool import BaseTool



class HttpToolConfig(BaseModel):
    name: str
    description: Optional[str]
    scheme: Literal["http", "https"] = "http"
    host: str
    port: int
    path: str = Field(default="/")
    method: Optional[Literal["GET", "POST", "PUT", "DELETE", "PATCH"]] = "POST"
    headers: Optional[dict[str, Any]]
    json_schema: dict[str, Any]
    return_type: Optional[Literal["text", "json"]] = "text"

class HttpTool(BaseTool[BaseModel, Any], Component[HttpToolConfig]):

    component_type = "tool"
    component_provider_override = "agen.tools.HttpTool"
    component_config_schema = HttpToolConfig

    def __init__(
        self,
        name: str,
        host: str,
        port: int,
        json_schema: dict[str, Any],
        headers: Optional[dict[str, Any]] = None,
        description: str = "HTTP tool",
        path: str = "/",
        scheme: Literal["http", "https"] = "http",
        method: Literal["GET", "POST", "PUT", "DELETE", "PATCH"] = "POST",
        return_type: Literal["text", "json"] = "text",
    ) -> None:
        self.server_params = HttpToolConfig(
            name=name,
            description=description,
            host=host,
            port=port,
            path=path,
            scheme=scheme,
            method=method,
            headers=headers,
            json_schema=json_schema,
            return_type=return_type,
        )

        path_params = {match.group(1) for match in re.finditer(r"{([^}]*)}", path)}
        self._path_params = path_params

        input_model = create_model(json_schema)

        base_return_type: Type[Any] = object

        super().__init__(input_model, base_return_type, name, description)

    def _to_config(self) -> HttpToolConfig:
        copied_config = self.server_params.model_copy()
        return copied_config

    @classmethod
    def _from_config(cls, config: HttpToolConfig) -> Self:
        copied_config = config.model_copy().model_dump()
        return cls(**copied_config)

    async def run(self, args: BaseModel, cancellation_token: CancellationToken) -> Any:

        model_dump = args.model_dump()
        path_params = {k: v for k, v in model_dump.items() if k in self._path_params}
        for k in self._path_params:
            model_dump.pop(k)

        path = self.server_params.path.format(**path_params)

        url = httpx.URL(
            scheme=self.server_params.scheme,
            host=self.server_params.host,
            port=self.server_params.port,
            path=path,
        )
        async with httpx.AsyncClient() as client:
            match self.server_params.method:
                case "GET":
                    response = await client.get(url, headers=self.server_params.headers, params=model_dump)
                case "PUT":
                    response = await client.put(url, headers=self.server_params.headers, json=model_dump)
                case "DELETE":
                    response = await client.delete(url, headers=self.server_params.headers, params=model_dump)
                case "PATCH":
                    response = await client.patch(url, headers=self.server_params.headers, json=model_dump)
                case _:  # Default case POST
                    response = await client.post(url, headers=self.server_params.headers, json=model_dump)

        match self.server_params.return_type:
            case "text":
                return response.text
            case "json":
                return response.json()
            case _:
                raise ValueError(f"Invalid return type: {self.server_params.return_type}")
