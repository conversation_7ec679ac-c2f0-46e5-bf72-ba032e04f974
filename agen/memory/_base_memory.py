from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, List, Union

from pydantic import BaseModel, ConfigDict, field_serializer

from agen.base import CancellationToken
from agen.base.component import ComponentBase
from agen.utils import Image
from agen.models.model_context import ChatCompletionContext


class MemoryMimeType(Enum):
    TEXT = "text/plain"
    JSON = "application/json"
    MARKDOWN = "text/markdown"
    IMAGE = "image/*"
    BINARY = "application/octet-stream"


ContentType = Union[str, bytes, Dict[str, Any], Image]


class MemoryContent(BaseModel):
    content: ContentType
    mime_type: MemoryMimeType | str
    metadata: Dict[str, Any] | None = None

    model_config = ConfigDict(arbitrary_types_allowed=True)

    @field_serializer("mime_type")
    def serialize_mime_type(self, mime_type: MemoryMimeType | str) -> str:
        if isinstance(mime_type, MemoryMimeType):
            return mime_type.value
        return mime_type


class MemoryQueryResult(BaseModel):
    results: List[MemoryContent]


class UpdateContextResult(BaseModel):
    memories: MemoryQueryResult


class Memory(ABC, ComponentBase[BaseModel]):
    component_type = "memory"

    @abstractmethod
    async def update_context(
        self,
        model_context: ChatCompletionContext,
    ) -> UpdateContextResult:
        ...

    @abstractmethod
    async def query(
        self,
        query: str | MemoryContent,
        cancellation_token: CancellationToken | None = None,
        **kwargs: Any,
    ) -> MemoryQueryResult:
        ...

    @abstractmethod
    async def add(self, content: MemoryContent, cancellation_token: CancellationToken | None = None) -> None:
        ...

    @abstractmethod
    async def clear(self) -> None:
        ...

    @abstractmethod
    async def close(self) -> None:
        ...
