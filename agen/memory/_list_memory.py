from typing import Any, List

from pydantic import BaseModel, Field
from typing_extensions import Self

from agen.base import CancellationToken
from agen.base.component import Component
from agen.models.model_context import ChatCompletionContext
from agen.models import SystemMessage
from ._base_memory import Memory, MemoryContent, MemoryQueryResult, UpdateContextResult


class ListMemoryConfig(BaseModel):
    name: str | None = None
    memory_contents: List[MemoryContent] = Field(default_factory=list)



class ListMemory(Memory, Component[ListMemoryConfig]):

    component_type = "memory"
    component_provider_override = "agen.memory.ListMemory"
    component_config_schema = ListMemoryConfig

    def __init__(self, name: str | None = None, memory_contents: List[MemoryContent] | None = None) -> None:
        self._name = name or "default_list_memory"
        self._contents: List[MemoryContent] = memory_contents if memory_contents is not None else []

    @property
    def name(self) -> str:
        return self._name

    @property
    def content(self) -> List[MemoryContent]:
        return self._contents

    @content.setter
    def content(self, value: List[MemoryContent]) -> None:
        self._contents = value

    async def update_context(
        self,
        model_context: ChatCompletionContext,
    ) -> UpdateContextResult:

        if not self._contents:
            return UpdateContextResult(memories=MemoryQueryResult(results=[]))

        memory_strings = [f"{i}. {str(memory.content)}" for i, memory in enumerate(self._contents, 1)]

        if memory_strings:
            memory_context = "\nRelevant memory content (in chronological order):\n"  + "\n".join(memory_strings) + "\n"
            await model_context.add_message(SystemMessage(content=memory_context))

        return UpdateContextResult(memories=MemoryQueryResult(results=self._contents))

    async def query(
        self,
        query: str | MemoryContent = "",
        cancellation_token: CancellationToken | None = None,
        **kwargs: Any,
    ) -> MemoryQueryResult:
        _ = query, cancellation_token, kwargs
        return MemoryQueryResult(results=self._contents)

    async def add(self, content: MemoryContent, cancellation_token: CancellationToken | None = None) -> None:
        self._contents.append(content)

    async def clear(self) -> None:
        self._contents = []

    async def close(self) -> None:
        pass

    @classmethod
    def _from_config(cls, config: ListMemoryConfig) -> Self:
        return cls(name=config.name, memory_contents=config.memory_contents)

    def _to_config(self) -> ListMemoryConfig:
        return ListMemoryConfig(name=self.name, memory_contents=self._contents)
