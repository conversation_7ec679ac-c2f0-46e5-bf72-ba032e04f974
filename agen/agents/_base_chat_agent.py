from abc import ABC, abstractmethod
from typing import Any, AsyncGenerator, List, Mapping, Sequence

from agen.base import CancellationToken
from agen.base.component import  ComponentBase
from agen.base.telemetry import trace_create_agent_span, trace_invoke_agent_span
from pydantic import BaseModel

from ._chat_agent import ChatAgent, Response
from agen.base.task import TaskResult
from agen.messages import (
    BaseAgentEvent,
    BaseChatMessage,
    ModelClientStreamingChunkEvent,
    TextMessage,
)
from agen.state import BaseState

class BaseChatAgent(ChatAgent, ABC, ComponentBase[BaseModel]):
    component_type = "agent"

    def __init__(self, name: str, description: str) -> None:
        with trace_create_agent_span(
            agent_name=name,
            agent_description=description,
        ):
            self._name = name
            if self._name.isidentifier() is False:
                raise ValueError("The agent name must be a valid Python identifier.")
            self._description = description



    @property
    def name(self) -> str:
        return self._name

    @property
    def description(self) -> str:
        return self._description

    @property
    @abstractmethod
    def produced_message_types(self) -> Sequence[type[BaseChatMessage]]:
        ...

    @abstractmethod
    async def on_messages(self, messages: Sequence[BaseChatMessage], cancellation_token: CancellationToken) -> Response:
        ...

    async def on_messages_stream(
        self, messages: Sequence[BaseChatMessage], cancellation_token: CancellationToken
    ) -> AsyncGenerator[BaseAgentEvent | BaseChatMessage | Response, None]:
        response = await self.on_messages(messages, cancellation_token)
        for inner_message in response.inner_messages or []:
            yield inner_message
        yield response


    async def run(
        self,
        *,
        task: str | BaseChatMessage | Sequence[BaseChatMessage] | None = None,
        cancellation_token: CancellationToken | None = None,
        output_task_messages: bool = True,
    ) -> TaskResult:

        with trace_invoke_agent_span(
            agent_name=self.name,
            agent_description=self.description,
        ):
            if cancellation_token is None:
                cancellation_token = CancellationToken()
            input_messages: List[BaseChatMessage] = []
            output_messages: List[BaseAgentEvent | BaseChatMessage] = []
            if task is None:
                pass

            elif isinstance(task, str):
                text_msg = TextMessage(content=task, source="user")
                input_messages.append(text_msg)
                if output_task_messages:
                    output_messages.append(text_msg)

            elif isinstance(task, BaseChatMessage):
                input_messages.append(task)
                if output_task_messages:
                    output_messages.append(task)

            else:
                if not task:
                    raise ValueError("Task list cannot be empty.")
                # Task is a sequence of messages.
                for msg in task:
                    if isinstance(msg, BaseChatMessage):
                        input_messages.append(msg)
                        if output_task_messages:
                            output_messages.append(msg)
                    else:
                        raise ValueError(f"Invalid message type in sequence: {type(msg)}")


            response = await self.on_messages(input_messages, cancellation_token)
            if response.inner_messages is not None:
                output_messages += response.inner_messages
            output_messages.append(response.chat_message)
            return TaskResult(messages=output_messages)

    async def run_stream(
        self,
        *,
        task: str | BaseChatMessage | Sequence[BaseChatMessage] | None = None,
        cancellation_token: CancellationToken | None = None,
        output_task_messages: bool = True,
    ) -> AsyncGenerator[BaseAgentEvent | BaseChatMessage | TaskResult, None]:

        with trace_invoke_agent_span(
            agent_name=self.name,
            agent_description=self.description,
        ):
            if cancellation_token is None:
                cancellation_token = CancellationToken()
            input_messages: List[BaseChatMessage] = []
            output_messages: List[BaseAgentEvent | BaseChatMessage] = []

            if task is None:
                pass
            elif isinstance(task, str):
                text_msg = TextMessage(content=task, source="user")
                input_messages.append(text_msg)
                if output_task_messages:
                    output_messages.append(text_msg)
                    yield text_msg
            elif isinstance(task, BaseChatMessage):
                input_messages.append(task)
                if output_task_messages:
                    output_messages.append(task)
                    yield task
            else:
                if not task:
                    raise ValueError("Task list cannot be empty.")
                for msg in task:
                    if isinstance(msg, BaseChatMessage):
                        input_messages.append(msg)
                        if output_task_messages:
                            output_messages.append(msg)
                            yield msg
                    else:
                        raise ValueError(f"Invalid message type in sequence: {type(msg)}")



            async for message in self.on_messages_stream(input_messages, cancellation_token):
                if isinstance(message, Response):
                    yield message.chat_message
                    output_messages.append(message.chat_message)
                    yield TaskResult(messages=output_messages)
                else:
                    yield message
                    if isinstance(message, ModelClientStreamingChunkEvent):
                        # Skip the model client streaming chunk events.
                        continue
                    output_messages.append(message)


    @abstractmethod
    async def on_reset(self, cancellation_token: CancellationToken) -> None:
        ...


    async def on_pause(self, cancellation_token: CancellationToken) -> None:
        pass

    async def on_resume(self, cancellation_token: CancellationToken) -> None:
        pass


    async def save_state(self) -> Mapping[str, Any]:
        return BaseState().model_dump()

    async def load_state(self, state: Mapping[str, Any]) -> None:
        BaseState.model_validate(state)

    async def close(self) -> None:
        pass
