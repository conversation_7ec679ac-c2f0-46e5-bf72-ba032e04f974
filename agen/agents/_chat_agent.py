from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, AsyncGenerator, Mapping, Sequence
from pydantic import BaseModel
from agen.base import CancellationToken
from agen.base.component import  ComponentBase
from agen.base.task import TaskRunner
from agen.messages import BaseAgentEvent, BaseChatMessage



@dataclass(kw_only=True)
class Response:
    chat_message: BaseChatMessage # 最终的回复消息
    inner_messages: Sequence[BaseAgentEvent | BaseChatMessage] | None = None

class ChatAgent(ABC, TaskRunner, ComponentBase[BaseModel]): #


    component_type = "agent"


    @property
    @abstractmethod
    def name(self) -> str:  # agent name unique for team
        ...

    @property
    @abstractmethod
    def description(self) -> str:
        ...

    @property
    @abstractmethod
    def produced_message_types(self) -> Sequence[type[BaseChatMessage]]:
        ...


    @abstractmethod
    async def on_messages(self, messages: Sequence[BaseChatMessage], cancellation_token: CancellationToken) -> Response:
        ...

    @abstractmethod
    def on_messages_stream(
        self, messages: Sequence[BaseChatMessage], cancellation_token: CancellationToken
    ) -> AsyncGenerator[BaseAgentEvent | BaseChatMessage | Response, None]:
        ...

    # 生命周期管理方法（抽象方法）
    @abstractmethod
    async def on_reset(self, cancellation_token: CancellationToken) -> None:
        ...

    @abstractmethod
    async def on_pause(self, cancellation_token: CancellationToken) -> None:
        ...

    @abstractmethod
    async def on_resume(self, cancellation_token: CancellationToken) -> None:
        ...


    @abstractmethod
    async def save_state(self) -> Mapping[str, Any]:
        ...

    @abstractmethod
    async def load_state(self, state: Mapping[str, Any]) -> None:
        ...

    @abstractmethod
    async def close(self) -> None:
        ...
